# Use Python 3.9 slim image as base
FROM python:3.9-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js (required for deemix)
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# Install deemix globally
RUN npm install -g deemix

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application files
COPY DeemixDownloader.py .
COPY web_app.py .
COPY templates/ templates/

# Create necessary directories
RUN mkdir -p config downloads

# Copy default config files
COPY config/ config/

# Create a non-root user for security
RUN useradd -m -u 1000 deemix && \
    chown -R deemix:deemix /app

# Switch to non-root user
USER deemix

# Expose port 10000
EXPOSE 10000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:10000/ || exit 1

# Start the web application
CMD ["python", "web_app.py"]
