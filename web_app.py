#!/usr/bin/env python3
"""
Flask web application for DeemixDownloader.py
Provides a simple web interface to run the script and view output in real-time.
"""

import os
import subprocess
import threading
import time
from flask import Flask, render_template, jsonify, request
from datetime import datetime
import json

app = Flask(__name__)

# Global variables to track script execution
script_running = False
script_output = []
script_process = None

def run_deemix_script():
    """Run the DeemixDownloader.py script and capture output"""
    global script_running, script_output, script_process
    
    script_running = True
    script_output = []
    
    try:
        # Add timestamp
        script_output.append(f"[{datetime.now().strftime('%H:%M:%S')}] Starting DeemixDownloader...")
        
        # Run the script
        script_process = subprocess.Popen(
            ['python', 'DeemixDownloader.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Read output line by line
        for line in iter(script_process.stdout.readline, ''):
            if line:
                timestamp = datetime.now().strftime('%H:%M:%S')
                script_output.append(f"[{timestamp}] {line.rstrip()}")
        
        script_process.wait()
        
        # Add completion message
        if script_process.returncode == 0:
            script_output.append(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ Script completed successfully!")
        else:
            script_output.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ Script failed with exit code {script_process.returncode}")
            
    except Exception as e:
        script_output.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ Error running script: {str(e)}")
    
    finally:
        script_running = False
        script_process = None

@app.route('/')
def index():
    """Main page with the run button"""
    return render_template('index.html')

@app.route('/run', methods=['POST'])
def run_script():
    """Start the DeemixDownloader script"""
    global script_running
    
    if script_running:
        return jsonify({'status': 'error', 'message': 'Script is already running'})
    
    # Start script in background thread
    thread = threading.Thread(target=run_deemix_script)
    thread.daemon = True
    thread.start()
    
    return jsonify({'status': 'success', 'message': 'Script started'})

@app.route('/status')
def get_status():
    """Get current script status and output"""
    return jsonify({
        'running': script_running,
        'output': script_output,
        'output_length': len(script_output)
    })

@app.route('/stop', methods=['POST'])
def stop_script():
    """Stop the running script"""
    global script_running, script_process
    
    if not script_running or script_process is None:
        return jsonify({'status': 'error', 'message': 'No script is running'})
    
    try:
        script_process.terminate()
        script_output.append(f"[{datetime.now().strftime('%H:%M:%S')}] ⚠️ Script stopped by user")
        return jsonify({'status': 'success', 'message': 'Script stopped'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': f'Error stopping script: {str(e)}'})

@app.route('/clear', methods=['POST'])
def clear_output():
    """Clear the output log"""
    global script_output
    script_output = []
    return jsonify({'status': 'success', 'message': 'Output cleared'})

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    
    # Run the Flask app
    app.run(host='0.0.0.0', port=10000, debug=False)
