#!/usr/bin/env python3
"""
Flask web application for DeemixDownloader.py
Provides a simple web interface to run the script and view output in real-time.
"""

import os
import subprocess
import threading
import time
from flask import Flask, render_template, jsonify, request, redirect, session
from datetime import datetime
import json
import spotipy
from spotipy.oauth2 import SpotifyOAuth
import secrets

app = Flask(__name__)
app.secret_key = secrets.token_hex(16)  # For session management

# Spotify configuration
SPOTIPY_CLIENT_ID = os.getenv('SPOTIPY_CLIENT_ID', '')
SPOTIPY_CLIENT_SECRET = os.getenv('SPOTIPY_CLIENT_SECRET', '')
SPOTIPY_REDIRECT_URI = os.getenv('SPOTIPY_REDIRECT_URI', 'http://localhost:10000/callback')
SPOTIFY_SCOPE = "user-library-read playlist-read-private playlist-read-collaborative"

# Global variables to track script execution
script_running = False
script_output = []
script_process = None

def run_deemix_script():
    """Run the DeemixDownloader.py script and capture output"""
    global script_running, script_output, script_process

    script_running = True
    script_output = []

    try:
        # Add timestamp
        script_output.append(f"[{datetime.now().strftime('%H:%M:%S')}] Starting DeemixDownloader...")

        # Check Spotify authentication first
        if not is_spotify_authenticated():
            script_output.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ Spotify not authenticated. Please authenticate first.")
            script_running = False
            return

        # Set up environment for Docker
        env = os.environ.copy()
        env['DOCKER_ENV'] = 'true'  # Tell the script it's running in Docker

        # Run the script
        script_process = subprocess.Popen(
            ['python', 'DeemixDownloader.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1,
            env=env
        )
        
        # Read output line by line
        for line in iter(script_process.stdout.readline, ''):
            if line:
                timestamp = datetime.now().strftime('%H:%M:%S')
                script_output.append(f"[{timestamp}] {line.rstrip()}")
        
        script_process.wait()
        
        # Add completion message
        if script_process.returncode == 0:
            script_output.append(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ Script completed successfully!")
        else:
            script_output.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ Script failed with exit code {script_process.returncode}")
            
    except Exception as e:
        script_output.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ Error running script: {str(e)}")
    
    finally:
        script_running = False
        script_process = None

def get_spotify_oauth():
    """Get Spotify OAuth manager"""
    return SpotifyOAuth(
        client_id=SPOTIPY_CLIENT_ID,
        client_secret=SPOTIPY_CLIENT_SECRET,
        redirect_uri=SPOTIPY_REDIRECT_URI,
        scope=SPOTIFY_SCOPE,
        cache_path="/app/config/.spotify_cache",
        show_dialog=False
    )

def is_spotify_authenticated():
    """Check if Spotify is authenticated"""
    if not SPOTIPY_CLIENT_ID or not SPOTIPY_CLIENT_SECRET:
        return False

    auth_manager = get_spotify_oauth()
    token_info = auth_manager.get_cached_token()
    return token_info is not None

@app.route('/')
def index():
    """Main page with the run button"""
    spotify_auth = is_spotify_authenticated()
    return render_template('index.html', spotify_authenticated=spotify_auth)

@app.route('/auth/spotify')
def spotify_auth():
    """Initiate Spotify authentication"""
    if not SPOTIPY_CLIENT_ID or not SPOTIPY_CLIENT_SECRET:
        return jsonify({'status': 'error', 'message': 'Spotify credentials not configured'})

    auth_manager = get_spotify_oauth()
    auth_url = auth_manager.get_authorize_url()
    return redirect(auth_url)

@app.route('/callback')
def spotify_callback():
    """Handle Spotify OAuth callback"""
    try:
        auth_manager = get_spotify_oauth()
        code = request.args.get('code')

        if code:
            # Exchange code for token
            token_info = auth_manager.get_access_token(code)
            if token_info:
                script_output.append(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ Spotify authentication successful!")
                return redirect('/?auth=success')

        return redirect('/?auth=error')
    except Exception as e:
        script_output.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ Spotify authentication failed: {str(e)}")
        return redirect('/?auth=error')

@app.route('/auth/status')
def auth_status():
    """Check authentication status"""
    return jsonify({
        'spotify_authenticated': is_spotify_authenticated(),
        'credentials_configured': bool(SPOTIPY_CLIENT_ID and SPOTIPY_CLIENT_SECRET)
    })

@app.route('/run', methods=['POST'])
def run_script():
    """Start the DeemixDownloader script"""
    global script_running
    
    if script_running:
        return jsonify({'status': 'error', 'message': 'Script is already running'})
    
    # Start script in background thread
    thread = threading.Thread(target=run_deemix_script)
    thread.daemon = True
    thread.start()
    
    return jsonify({'status': 'success', 'message': 'Script started'})

@app.route('/status')
def get_status():
    """Get current script status and output"""
    return jsonify({
        'running': script_running,
        'output': script_output,
        'output_length': len(script_output)
    })

@app.route('/stop', methods=['POST'])
def stop_script():
    """Stop the running script"""
    global script_running, script_process
    
    if not script_running or script_process is None:
        return jsonify({'status': 'error', 'message': 'No script is running'})
    
    try:
        script_process.terminate()
        script_output.append(f"[{datetime.now().strftime('%H:%M:%S')}] ⚠️ Script stopped by user")
        return jsonify({'status': 'success', 'message': 'Script stopped'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': f'Error stopping script: {str(e)}'})

@app.route('/clear', methods=['POST'])
def clear_output():
    """Clear the output log"""
    global script_output
    script_output = []
    return jsonify({'status': 'success', 'message': 'Output cleared'})

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    
    # Run the Flask app
    app.run(host='0.0.0.0', port=10000, debug=False)
