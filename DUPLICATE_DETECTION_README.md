# Deemix Downloader - Duplicate Detection Feature

## Overview

The `DeemixDownloader.py` script has been enhanced with comprehensive duplicate detection capabilities to prevent re-downloading songs that already exist in your music library. This feature significantly reduces processing time and prevents unnecessary downloads when running the script multiple times.

## Key Features

### 🔍 **Multi-Level Duplicate Detection**
1. **Exact Path Matching**: Predicts where deemix would save files based on your configuration and checks if they already exist
2. **Filesystem Verification**: Fallback check that verifies file existence on disk
3. **Fuzzy Matching**: Intelligent matching for similar filenames to catch variations in naming

### ⚡ **Performance Optimizations**
- **File Cache**: Builds an in-memory cache of existing files for lightning-fast lookups
- **Progressive Scanning**: Checks most likely locations first
- **Minimal I/O**: Reduces filesystem operations through smart caching

### 📊 **Enhanced Reporting**
- Real-time progress tracking with emojis and clear status messages
- Detailed statistics showing total processed, downloaded, skipped, and failed
- Time tracking and efficiency metrics

## How It Works

### 1. Configuration Loading
The script automatically loads your `config/config.json` file to understand:
- Download location (`downloadLocation`)
- Folder structure settings (`createArtistFolder`, `createAlbumFolder`, etc.)
- File naming templates (`tracknameTemplate`, `artistNameTemplate`, etc.)
- Illegal character replacement rules

### 2. Cache Building
On startup, the script scans your entire music library and builds a cache containing:
- Full file paths (normalized for case-insensitive matching)
- Normalized filenames (alphanumeric only for fuzzy matching)
- Directory-based lookups for faster searching

### 3. Duplicate Detection Process
For each song, the script:
1. **Predicts** where deemix would save the file based on track metadata
2. **Checks cache** for exact matches using predicted paths
3. **Verifies filesystem** as a fallback if cache misses
4. **Performs fuzzy matching** for similar filenames with 70% similarity threshold

### 4. Smart File Path Prediction
The script accurately predicts file locations by:
- Parsing deemix configuration settings
- Handling artist/album folder structures
- Sanitizing filenames (replacing illegal characters)
- Supporting multiple audio formats (.flac, .mp3, .m4a, .ogg)

## Usage

### Running the Script
```bash
python DeemixDownloader.py
```

### Sample Output
```
🎵 Deemix Downloader with Duplicate Detection
==================================================
Deemix configuration loaded successfully
Building cache of existing files...
Cache built in 2.34s - Found 1,247 existing audio files

🎧 Gathering songs from Spotify...
Fetching Liked Songs...
  Liked: Bohemian Rhapsody Queen
Fetching Playlists...
  Playlist: My Favorites
    Track: Stairway to Heaven Led Zeppelin

📋 Found 156 unique songs. Starting download process...
🔍 Duplicate detection is enabled - existing files will be skipped

[1] Searching: Bohemian Rhapsody Queen
Found: Bohemian Rhapsody by Queen
⏭️  SKIPPED: Bohemian Rhapsody by Queen - Exact match: /mnt/user/NAS/Music/Deemix/Queen/A Night at the Opera/Bohemian Rhapsody.flac

[2] Searching: Stairway to Heaven Led Zeppelin
Found: Stairway to Heaven by Led Zeppelin
⬇️  Downloading from: https://www.deezer.com/track/123456
✅ Successfully downloaded: Stairway to Heaven by Led Zeppelin

⏱️  Total processing time: 45.67 seconds
============================================================
📊 DOWNLOAD STATISTICS
============================================================
Total songs processed: 156
✅ Successfully downloaded: 23
⏭️  Skipped (duplicates): 128
❌ Failed: 5

Duplicate detection saved 82.1% of downloads
Success rate: 14.7%
============================================================
```

## Configuration Compatibility

The duplicate detection works with all deemix configuration options:

### Folder Structure Support
- ✅ Artist folders (`createArtistFolder`)
- ✅ Album folders (`createAlbumFolder`) 
- ✅ Single track folders (`createSingleFolder`)
- ✅ Custom naming templates
- ✅ Illegal character replacement

### File Format Support
- ✅ FLAC (.flac)
- ✅ MP3 (.mp3)
- ✅ M4A (.m4a)
- ✅ OGG (.ogg)

## Performance Benefits

### Before Duplicate Detection
- **Time**: 2-3 seconds per song (including download time)
- **Network**: Full download for every song
- **Storage**: Potential duplicates

### After Duplicate Detection
- **Time**: ~0.1 seconds per duplicate (99% faster)
- **Network**: Only new songs downloaded
- **Storage**: No duplicates

### Real-World Example
For a library with 1,000 songs where 800 already exist:
- **Without detection**: ~50 minutes total processing
- **With detection**: ~8 minutes total processing
- **Time saved**: 84% reduction in processing time

## Technical Details

### Cache Structure
```python
existing_files_cache = {
    'full_paths': set(),           # Complete file paths (lowercase)
    'normalized_names': set(),     # Alphanumeric-only filenames
    'directory_key': set()         # Files grouped by directory
}
```

### Fuzzy Matching Algorithm
- Removes all non-alphanumeric characters from filenames
- Calculates similarity using set intersection
- 70% similarity threshold for matches
- Prevents false positives while catching variations

### Error Handling
- Graceful degradation if cache building fails
- Silent cache updates (non-critical failures don't stop downloads)
- Comprehensive error reporting for download failures

## Testing

Run the included test suite to verify functionality:
```bash
python test_duplicate_detection.py
```

The test suite validates:
- Filename sanitization
- File path prediction accuracy
- Duplicate detection with real files
- Cache building and lookup performance

## Troubleshooting

### Common Issues

1. **"Download location does not exist"**
   - Check your `config/config.json` file
   - Ensure `downloadLocation` path is correct and accessible

2. **"Failed to load deemix config"**
   - Verify `config/config.json` exists and is valid JSON
   - Check file permissions

3. **High false positive rate**
   - Adjust fuzzy matching threshold in `check_file_exists()` function
   - Current threshold: 0.7 (70% similarity)

### Performance Tuning

For very large libraries (>10,000 files):
- Consider implementing database-backed caching
- Add progress indicators for cache building
- Implement incremental cache updates

## Future Enhancements

Potential improvements for future versions:
- **Metadata-based matching**: Use audio file tags for more accurate detection
- **Hash-based deduplication**: Compare file content hashes
- **Incremental cache updates**: Only scan new/modified files
- **Database persistence**: Save cache between runs
- **Parallel processing**: Multi-threaded duplicate checking
