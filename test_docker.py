#!/usr/bin/env python3
"""
Test script to verify Docker container functionality
"""

import requests
import time
import sys

def test_web_interface():
    """Test if the web interface is accessible"""
    try:
        print("🧪 Testing web interface...")
        response = requests.get('http://localhost:10000', timeout=10)
        if response.status_code == 200:
            print("✅ Web interface is accessible")
            return True
        else:
            print(f"❌ Web interface returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to connect to web interface: {e}")
        return False

def test_status_endpoint():
    """Test the status API endpoint"""
    try:
        print("🧪 Testing status endpoint...")
        response = requests.get('http://localhost:10000/status', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status endpoint working - Running: {data.get('running', 'unknown')}")
            return True
        else:
            print(f"❌ Status endpoint returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to connect to status endpoint: {e}")
        return False
    except ValueError as e:
        print(f"❌ Invalid JSON response from status endpoint: {e}")
        return False

def test_container_health():
    """Test container health"""
    try:
        print("🧪 Testing container health...")
        # Give container time to start if just launched
        for i in range(30):
            try:
                response = requests.get('http://localhost:10000/', timeout=5)
                if response.status_code == 200:
                    print("✅ Container is healthy and responding")
                    return True
            except:
                pass
            
            if i < 29:  # Don't sleep on last iteration
                print(f"   Waiting for container... ({i+1}/30)")
                time.sleep(2)
        
        print("❌ Container failed to respond within 60 seconds")
        return False
    except Exception as e:
        print(f"❌ Error testing container health: {e}")
        return False

def main():
    """Run all tests"""
    print("🐳 DeemixDownloader Docker Container Test")
    print("=" * 45)
    
    tests = [
        test_container_health,
        test_web_interface,
        test_status_endpoint
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 45)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Container is working correctly.")
        print("🌐 You can now access the web interface at: http://localhost:10000")
        return 0
    else:
        print("❌ Some tests failed. Check the container logs:")
        print("   docker-compose logs -f")
        return 1

if __name__ == "__main__":
    sys.exit(main())
