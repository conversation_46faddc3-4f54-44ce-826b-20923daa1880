#!/bin/bash

# DeemixDownloader Docker Startup Script

echo "🎵 Starting DeemixDownloader Docker Container"
echo "=============================================="

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  Warning: .env file not found!"
    echo "   Please copy .env.example to .env and add your Spotify credentials"
    echo "   cp .env.example .env"
    echo ""
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Create downloads directory if it doesn't exist
mkdir -p downloads

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose not found. Please install docker-compose."
    exit 1
fi

echo "🚀 Starting container with docker-compose..."
docker-compose up -d

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Container started successfully!"
    echo ""
    echo "🌐 Web Interface: http://localhost:10000"
    echo "📊 Health Check: http://localhost:10000/status"
    echo ""
    echo "📋 Useful commands:"
    echo "   View logs:    docker-compose logs -f"
    echo "   Stop:         docker-compose down"
    echo "   Restart:      docker-compose restart"
    echo ""
    echo "📁 Downloads will be saved to: ./downloads/"
    echo ""
    
    # Wait a moment for container to start
    sleep 3
    
    # Check if container is healthy
    if curl -s http://localhost:10000/ > /dev/null; then
        echo "🎉 Container is running and web interface is accessible!"
        echo "   Opening browser..."
        
        # Try to open browser (works on most systems)
        if command -v xdg-open > /dev/null; then
            xdg-open http://localhost:10000
        elif command -v open > /dev/null; then
            open http://localhost:10000
        elif command -v start > /dev/null; then
            start http://localhost:10000
        else
            echo "   Please open http://localhost:10000 in your browser"
        fi
    else
        echo "⚠️  Container started but web interface not yet ready"
        echo "   Please wait a moment and try: http://localhost:10000"
    fi
else
    echo "❌ Failed to start container"
    echo "   Check the logs with: docker-compose logs"
    exit 1
fi
