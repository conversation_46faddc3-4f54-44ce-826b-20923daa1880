import os
import subprocess
import spotipy
from spotipy.oauth2 import SpotifyOAuth
import deezer
import json
import re
from collections import defaultdict
import time
from datetime import datetime

# ---- Config ----
# Get Spotify credentials from environment variables or use defaults
SPOTIPY_CLIENT_ID = os.getenv('SPOTIPY_CLIENT_ID', '57f12a98a5aa492cb8ce0e29d6065555')
SPOTIPY_CLIENT_SECRET = os.getenv('SPOTIPY_CLIENT_SECRET', '97b45bb40d3f4cd393e4c55abe3cba5f')
SPOTIPY_REDIRECT_URI = os.getenv('SPOTIPY_REDIRECT_URI', 'http://127.0.0.1:8888/callback')

scope = "user-library-read playlist-read-private playlist-read-collaborative"

# Initialize Spotify client (will be done in main function to handle errors better)
sp = None

# Initialize Deezer API (done once)
dz = None

# Global variables for duplicate detection
deemix_config = None
existing_files_cache = defaultdict(set)
download_stats = {
    'total': 0,
    'skipped': 0,
    'downloaded': 0,
    'failed': 0
}

# Global list to track failed downloads
failed_downloads = []

def load_deemix_config():
    """Load deemix configuration to understand file structure"""
    global deemix_config
    if deemix_config is None:
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'config', 'config.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                deemix_config = json.load(f)
            print("Deemix configuration loaded successfully")
            return True
        except Exception as e:
            print(f"Failed to load deemix config: {e}")
            return False
    return True

def init_spotify():
    """Initialize Spotify API client with Docker-friendly authentication"""
    global sp
    if sp is None:
        try:
            # Check if we're running in Docker (no display available)
            is_docker = os.getenv('DOCKER_ENV', 'false').lower() == 'true' or not os.getenv('DISPLAY')

            if is_docker:
                print("🐳 Docker environment detected - using cache-based authentication")
                # Use cache directory that persists in Docker
                cache_path = "/app/config/.spotify_cache"

                # Try to use cached token first
                auth_manager = SpotifyOAuth(
                    client_id=SPOTIPY_CLIENT_ID,
                    client_secret=SPOTIPY_CLIENT_SECRET,
                    redirect_uri=SPOTIPY_REDIRECT_URI,
                    scope=scope,
                    cache_path=cache_path,
                    open_browser=False,  # Don't try to open browser in Docker
                    show_dialog=False
                )

                # Check if we have a valid cached token
                token_info = auth_manager.get_cached_token()
                if token_info:
                    print("✅ Using cached Spotify authentication")
                    sp = spotipy.Spotify(auth_manager=auth_manager)
                else:
                    print("❌ No cached Spotify token found!")
                    print("🔧 To authenticate Spotify in Docker:")
                    print("   1. Run the script locally first to authenticate")
                    print("   2. Copy the .cache file to config/.spotify_cache")
                    print("   3. Or use the web interface authentication flow")
                    return False
            else:
                print("💻 Local environment detected - using interactive authentication")
                # Standard OAuth flow for local development
                auth_manager = SpotifyOAuth(
                    client_id=SPOTIPY_CLIENT_ID,
                    client_secret=SPOTIPY_CLIENT_SECRET,
                    redirect_uri=SPOTIPY_REDIRECT_URI,
                    scope=scope
                )
                sp = spotipy.Spotify(auth_manager=auth_manager)

            # Test the connection
            try:
                user = sp.current_user()
                print(f"✅ Spotify API initialized successfully for user: {user['display_name']}")
                return True
            except Exception as e:
                print(f"❌ Failed to connect to Spotify API: {e}")
                return False

        except Exception as e:
            print(f"❌ Failed to initialize Spotify API: {e}")
            return False
    return True

def init_deezer():
    """Initialize Deezer API if not already done"""
    global dz
    if dz is None:
        try:
            # Create Deezer instance for searching
            dz = deezer.Deezer()
            print("Deezer API initialized successfully")
            return True
        except Exception as e:
            print(f"Failed to initialize Deezer API: {e}")
            return False
    return True

def sanitize_filename(filename, illegal_char_replacer="_"):
    """Sanitize filename by replacing illegal characters"""
    # Common illegal characters for filenames
    illegal_chars = r'[<>:"/\\|?*]'
    return re.sub(illegal_chars, illegal_char_replacer, filename)

def predict_file_path(track_info, config):
    """Predict where deemix would save the file based on track info and config"""
    try:
        download_location = config['downloadLocation']

        # Extract track information
        title = track_info.get('title', 'Unknown Title')
        artist = track_info.get('artist', {}).get('name', 'Unknown Artist')
        album = track_info.get('album', {}).get('title', 'Unknown Album')

        # Sanitize names
        illegal_replacer = config.get('illegalCharacterReplacer', '_')
        title = sanitize_filename(title, illegal_replacer)
        artist = sanitize_filename(artist, illegal_replacer)
        album = sanitize_filename(album, illegal_replacer)

        # Build path based on config settings
        path_parts = [download_location]

        # Add artist folder if enabled
        if config.get('createArtistFolder', False):
            artist_template = config.get('artistNameTemplate', '%artist%')
            artist_folder = artist_template.replace('%artist%', artist)
            path_parts.append(artist_folder)

        # Add album folder if enabled
        if config.get('createAlbumFolder', False):
            album_template = config.get('albumNameTemplate', '%album%')
            album_folder = album_template.replace('%album%', album)
            path_parts.append(album_folder)

        # Add single folder if enabled and no album folder
        if config.get('createSingleFolder', False) and not config.get('createAlbumFolder', False):
            path_parts.append('Singles')

        # Generate filename
        track_template = config.get('tracknameTemplate', '%title%')
        filename = track_template.replace('%title%', title).replace('%artist%', artist)

        # Common audio file extensions
        possible_extensions = ['.flac', '.mp3', '.m4a', '.ogg']

        # Return list of possible file paths
        possible_paths = []
        for ext in possible_extensions:
            full_path = os.path.join(*path_parts, filename + ext)
            possible_paths.append(full_path)

        return possible_paths

    except Exception as e:
        print(f"Error predicting file path: {e}")
        return []

def build_existing_files_cache(download_location):
    """Build a cache of existing files for fast duplicate detection"""
    global existing_files_cache

    if not os.path.exists(download_location):
        print(f"Download location does not exist: {download_location}")
        return

    print("Building cache of existing files...")
    start_time = time.time()

    # Clear existing cache
    existing_files_cache.clear()

    # Walk through all directories and cache files
    for root, _, files in os.walk(download_location):
        for file in files:
            if file.lower().endswith(('.mp3', '.flac', '.m4a', '.ogg')):
                full_path = os.path.join(root, file)
                # Store both full path and normalized filename for fuzzy matching
                existing_files_cache['full_paths'].add(full_path.lower())

                # Create normalized filename for fuzzy matching
                normalized_name = re.sub(r'[^a-zA-Z0-9]', '', file.lower())
                existing_files_cache['normalized_names'].add(normalized_name)

                # Store directory-based lookup for faster searching
                dir_key = os.path.dirname(full_path).lower()
                existing_files_cache[dir_key].add(file.lower())

    elapsed = time.time() - start_time
    total_files = len(existing_files_cache['full_paths'])
    print(f"Cache built in {elapsed:.2f}s - Found {total_files} existing audio files")

def check_file_exists(track_info, config):
    """Check if a file already exists using multiple detection methods"""

    # Method 1: Exact path prediction
    predicted_paths = predict_file_path(track_info, config)
    for path in predicted_paths:
        if path.lower() in existing_files_cache['full_paths']:
            return True, f"Exact match: {path}"

    # Method 2: Check if files exist in filesystem (fallback)
    for path in predicted_paths:
        if os.path.exists(path):
            return True, f"File exists: {path}"

    # Method 3: Fuzzy matching based on normalized names
    title = track_info.get('title', '')
    artist = track_info.get('artist', {}).get('name', '')

    # Create normalized search string
    search_string = re.sub(r'[^a-zA-Z0-9]', '', f"{artist}{title}".lower())

    # Check for fuzzy matches
    for normalized_name in existing_files_cache['normalized_names']:
        if search_string in normalized_name or normalized_name in search_string:
            # Additional similarity check
            similarity = len(set(search_string) & set(normalized_name)) / max(len(search_string), len(normalized_name), 1)
            if similarity > 0.7:  # 70% similarity threshold
                return True, f"Fuzzy match found (similarity: {similarity:.2f})"

    return False, "No duplicate found"

def update_cache_with_new_file(track_info, config):
    """Update the cache with a newly downloaded file"""
    try:
        predicted_paths = predict_file_path(track_info, config)
        for path in predicted_paths:
            if os.path.exists(path):
                # Add to cache
                existing_files_cache['full_paths'].add(path.lower())

                # Add normalized name
                filename = os.path.basename(path)
                normalized_name = re.sub(r'[^a-zA-Z0-9]', '', filename.lower())
                existing_files_cache['normalized_names'].add(normalized_name)

                # Add to directory cache
                dir_key = os.path.dirname(path).lower()
                existing_files_cache[dir_key].add(filename.lower())
                break
    except Exception:
        # Silent fail - cache update is not critical
        pass

def record_failed_download(query, reason):
    """Record a failed download with query and reason"""
    global failed_downloads
    failed_downloads.append({
        'query': query,
        'reason': reason,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    })

def deemix_search_and_download(query):
    global download_stats
    download_stats['total'] += 1

    print(f"[{download_stats['total']}] Searching: {query}")

    # Initialize Deezer API and config if needed
    if not init_deezer():
        reason = "Deezer API initialization failed"
        print(f"Failed to download '{query}': {reason}")
        record_failed_download(query, reason)
        download_stats['failed'] += 1
        return

    if not load_deemix_config():
        reason = "Config loading failed"
        print(f"Failed to download '{query}': {reason}")
        record_failed_download(query, reason)
        download_stats['failed'] += 1
        return

    try:
        # Search for the track on Deezer
        search_result = dz.api.search(query, limit=1)

        if not search_result['data']:
            reason = "No results found on Deezer"
            print(f"No results found for '{query}'")
            record_failed_download(query, reason)
            download_stats['failed'] += 1
            return

        # Get the first track result
        track = search_result['data'][0]

        print(f"Found: {track['title']} by {track['artist']['name']}")

        # Check for duplicates before downloading
        exists, reason = check_file_exists(track, deemix_config)
        if exists:
            print(f"⏭️  SKIPPED: {track['title']} by {track['artist']['name']} - {reason}")
            download_stats['skipped'] += 1
            return

        track_link = f"https://www.deezer.com/track/{track['id']}"
        print(f"⬇️  Downloading from: {track_link}")

        # Use deemix command-line tool with just the URL (config.json handles all settings)
        subprocess.run([
            'deemix',
            '--portable',
            track_link
        ], check=True)

        print(f"✅ Successfully downloaded: {track['title']} by {track['artist']['name']}")
        download_stats['downloaded'] += 1

        # Update cache with newly downloaded file
        update_cache_with_new_file(track, deemix_config)

    except subprocess.CalledProcessError as e:
        reason = f"Download process failed: {str(e)}"
        print(f"❌ Failed to download '{query}': {e}")
        record_failed_download(query, reason)
        download_stats['failed'] += 1
    except Exception as e:
        reason = f"Unexpected error: {str(e)}"
        print(f"❌ Failed to download '{query}': {e}")
        record_failed_download(query, reason)
        download_stats['failed'] += 1

def print_download_statistics():
    """Print final download statistics"""
    print("\n" + "="*60)
    print("📊 DOWNLOAD STATISTICS")
    print("="*60)
    print(f"Total songs processed: {download_stats['total']}")
    print(f"✅ Successfully downloaded: {download_stats['downloaded']}")
    print(f"⏭️  Skipped (duplicates): {download_stats['skipped']}")
    print(f"❌ Failed: {download_stats['failed']}")

    if download_stats['total'] > 0:
        skip_percentage = (download_stats['skipped'] / download_stats['total']) * 100
        success_percentage = (download_stats['downloaded'] / download_stats['total']) * 100
        print(f"\nDuplicate detection saved {skip_percentage:.1f}% of downloads")
        print(f"Success rate: {success_percentage:.1f}%")
    print("="*60)

def print_failed_downloads():
    """Print failed downloads to console"""
    print("\n" + "="*60)
    print("❌ FAILED DOWNLOADS")
    print("="*60)

    if not failed_downloads:
        print("🎉 No failed downloads! All songs were either downloaded successfully or skipped as duplicates.")
    else:
        print(f"Total failed downloads: {len(failed_downloads)}\n")

        for i, failure in enumerate(failed_downloads, 1):
            print(f"{i}. Query: {failure['query']}")
            print(f"   Reason: {failure['reason']}")
            print(f"   Time: {failure['timestamp']}")
            print("-" * 40)

    print("="*60)

# ---- Spotify Helpers ----
def get_liked_tracks():
    liked = []
    results = sp.current_user_saved_tracks(limit=50)
    print("Fetching Liked Songs...")
    while results:
        for item in results['items']:
            track = item['track']
            track_str = f"{track['name']} {track['artists'][0]['name']}"
            print(f"  Liked: {track_str}")
            liked.append(track_str)
        if results['next']:
            results = sp.next(results)
        else:
            break
    return liked

def get_playlist_tracks():
    tracks = []
    playlists = sp.current_user_playlists()
    print("Fetching Playlists...")
    for playlist in playlists['items']:
        print(f"  Playlist: {playlist['name']}")
        results = sp.playlist_tracks(playlist['id'])
        for item in results['items']:
            track = item['track']
            if track:
                track_str = f"{track['name']} {track['artists'][0]['name']}"
                print(f"    Track: {track_str}")
                tracks.append(track_str)
    return tracks



# ---- Main ----
if __name__ == "__main__":
    print("🎵 Deemix Downloader with Duplicate Detection")
    print("=" * 50)

    # Initialize Spotify API
    if not init_spotify():
        print("❌ Failed to initialize Spotify API. Exiting.")
        exit(1)

    # Load configuration first
    if not load_deemix_config():
        print("❌ Failed to load configuration. Exiting.")
        exit(1)

    # Build cache of existing files
    download_location = deemix_config.get('downloadLocation', '')
    if download_location:
        build_existing_files_cache(download_location)
    else:
        print("⚠️  Warning: No download location found in config. Duplicate detection may be limited.")

    print("\n🎧 Gathering songs from Spotify...")
    all_tracks = set()
    all_tracks.update(get_liked_tracks())
    all_tracks.update(get_playlist_tracks())
    # Note: get_discovery_weekly() removed due to Spotify API changes (Nov 2024)
    # Discover Weekly and other algorithmic playlists are no longer accessible via API

    print(f"\n📋 Found {len(all_tracks)} unique songs. Starting download process...")
    print("🔍 Duplicate detection is enabled - existing files will be skipped\n")

    start_time = time.time()

    for track_query in all_tracks:
        deemix_search_and_download(track_query)

    # Print final statistics
    total_time = time.time() - start_time
    print(f"\n⏱️  Total processing time: {total_time:.2f} seconds")
    print_download_statistics()

    # Print failed downloads
    print_failed_downloads()
