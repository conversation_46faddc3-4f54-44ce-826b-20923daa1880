# DeemixDownloader Docker Makefile

.PHONY: help build up down logs restart test clean setup

# Default target
help:
	@echo "🎵 DeemixDownloader Docker Commands"
	@echo "=================================="
	@echo ""
	@echo "Setup:"
	@echo "  setup     - Initial setup (copy .env.example to .env)"
	@echo "  build     - Build the Docker image"
	@echo ""
	@echo "Container Management:"
	@echo "  up        - Start the container"
	@echo "  down      - Stop and remove the container"
	@echo "  restart   - Restart the container"
	@echo "  logs      - View container logs"
	@echo ""
	@echo "Testing:"
	@echo "  test      - Test the container functionality"
	@echo ""
	@echo "Maintenance:"
	@echo "  clean     - Remove container and images"
	@echo ""
	@echo "Quick Start:"
	@echo "  make setup && make up"

# Initial setup
setup:
	@echo "🔧 Setting up DeemixDownloader..."
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "✅ Created .env file from template"; \
		echo "⚠️  Please edit .env and add your Spotify credentials"; \
	else \
		echo "✅ .env file already exists"; \
	fi
	@mkdir -p downloads
	@echo "✅ Created downloads directory"

# Build the Docker image
build:
	@echo "🏗️  Building Docker image..."
	docker-compose build

# Start the container
up:
	@echo "🚀 Starting container..."
	docker-compose up -d
	@echo "✅ Container started!"
	@echo "🌐 Web interface: http://localhost:10000"

# Stop the container
down:
	@echo "⏹️  Stopping container..."
	docker-compose down

# View logs
logs:
	@echo "📋 Container logs (Ctrl+C to exit):"
	docker-compose logs -f

# Restart the container
restart:
	@echo "🔄 Restarting container..."
	docker-compose restart

# Test the container
test:
	@echo "🧪 Testing container..."
	@if command -v python3 >/dev/null 2>&1; then \
		python3 test_docker.py; \
	elif command -v python >/dev/null 2>&1; then \
		python test_docker.py; \
	else \
		echo "❌ Python not found. Please install Python to run tests."; \
		echo "   Alternatively, test manually at: http://localhost:10000"; \
	fi

# Clean up
clean:
	@echo "🧹 Cleaning up..."
	docker-compose down --rmi all --volumes --remove-orphans
	@echo "✅ Cleanup complete"

# Development helpers
dev-logs:
	@echo "📋 Development logs (with timestamps):"
	docker-compose logs -f --timestamps

dev-shell:
	@echo "🐚 Opening shell in container..."
	docker-compose exec deemix-downloader /bin/bash

dev-rebuild:
	@echo "🔨 Rebuilding and restarting..."
	docker-compose down
	docker-compose build --no-cache
	docker-compose up -d
