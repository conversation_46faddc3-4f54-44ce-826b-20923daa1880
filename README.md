# DeemixDownloader Docker Web Interface

A Docker container that provides a web interface for the DeemixDownloader.py script, allowing you to download your Spotify liked songs and playlists from <PERSON><PERSON> through a simple web UI.

## Features

- 🌐 **Web Interface**: Simple, modern web UI accessible on port 10000
- 🚀 **One-Click Execution**: Run the download script with a single button click
- 📊 **Real-time Output**: View script execution progress and output in real-time
- 📈 **Statistics Display**: See download statistics and failed downloads list
- 🐳 **Docker Ready**: Complete containerized solution with docker-compose
- 💾 **Persistent Storage**: Downloads and config persist outside the container
- 🔧 **Easy Configuration**: Environment variables for Spotify API credentials

## Quick Start

### Prerequisites

- Docker and Docker Compose installed
- Spotify Developer Account (for API credentials)

### 1. Get Spotify API Credentials

1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard/)
2. Create a new app
3. Note your `Client ID` and `Client Secret`
4. **Important**: Add `http://localhost:10000/callback` to your app's redirect URIs
   - This is crucial for <PERSON><PERSON> authentication to work properly

### 2. Setup Environment

```bash
# Clone or download the project files
git clone <repository-url>
cd deemix-downloader

# Copy the environment template
cp .env.example .env

# Edit .env with your Spotify credentials
nano .env
```

### 3. Run with Docker Compose

```bash
# Start the container
docker-compose up -d

# View logs
docker-compose logs -f
```

### 4. Access the Web Interface

Open your browser and go to: `http://localhost:10000`

## Manual Docker Run

If you prefer to run without docker-compose:

```bash
# Build the image
docker build -t deemix-downloader .

# Run the container
docker run -d \
  --name deemix-downloader \
  -p 10000:10000 \
  -v $(pwd)/downloads:/app/downloads \
  -v $(pwd)/config:/app/config \
  -e SPOTIPY_CLIENT_ID=your_client_id \
  -e SPOTIPY_CLIENT_SECRET=your_client_secret \
  deemix-downloader
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SPOTIPY_CLIENT_ID` | Your Spotify Client ID | Required |
| `SPOTIPY_CLIENT_SECRET` | Your Spotify Client Secret | Required |
| `SPOTIPY_REDIRECT_URI` | Spotify OAuth redirect URI | `http://127.0.0.1:8888/callback` |
| `TZ` | Container timezone | `UTC` |

### Volume Mounts

| Host Path | Container Path | Description |
|-----------|----------------|-------------|
| `./downloads` | `/app/downloads` | Downloaded music files |
| `./config` | `/app/config` | Deemix configuration files |

### Deemix Configuration

Edit `config/config.json` to customize download settings:

- **Download location**: Already set to `/app/downloads` for Docker
- **File naming**: Customize track and album naming templates
- **Quality settings**: Set maximum bitrate and fallback options
- **Folder structure**: Configure artist/album folder creation

## Web Interface Usage

### First Time Setup
1. **Authenticate Spotify**: Click "🎵 Authenticate Spotify" when prompted
2. **Login**: You'll be redirected to Spotify to authorize the app
3. **Return**: After authorization, you'll be redirected back to the interface

### Running Downloads
1. **Start Script**: Click "🚀 Run Script" to begin downloading (only available after authentication)
2. **Monitor Progress**: Watch real-time output in the console area
3. **Stop Script**: Use "⏹️ Stop Script" if needed
4. **Clear Output**: Click "🗑️ Clear Output" to clean the console

The interface shows:
- 🔐 Authentication status (required before running)
- ✅ Successfully downloaded tracks
- ⏭️ Skipped duplicates
- ❌ Failed downloads with reasons
- 📊 Final statistics summary

## Troubleshooting

### Common Issues

**"Failed to initialize Spotify API"**
- Check your Spotify credentials in `.env`
- Ensure redirect URI is added to your Spotify app

**"No results found on Deezer"**
- Some tracks may not be available on Deezer
- Try alternative search terms or check Deezer manually

**Permission errors**
- Ensure the downloads directory is writable
- Check Docker volume mount permissions

### Logs

View container logs:
```bash
docker-compose logs -f deemix-downloader
```

### Health Check

The container includes a health check accessible at:
```
http://localhost:10000/status
```

## File Structure

```
deemix-downloader/
├── DeemixDownloader.py    # Main download script
├── web_app.py            # Flask web application
├── Dockerfile            # Docker build instructions
├── docker-compose.yml    # Docker Compose configuration
├── requirements.txt      # Python dependencies
├── .env.example         # Environment variables template
├── templates/
│   └── index.html       # Web interface template
├── config/
│   └── config.json      # Deemix configuration
└── downloads/           # Downloaded music (created automatically)
```

## Security Notes

- The container runs as a non-root user for security
- Spotify credentials are passed via environment variables
- No sensitive data is stored in the Docker image
- Web interface is bound to all interfaces (0.0.0.0) - use reverse proxy for production

## Support

For issues related to:
- **Deemix**: Check the [official Deemix documentation](https://deemix.app/)
- **Spotify API**: See [Spotify Web API documentation](https://developer.spotify.com/documentation/web-api/)
- **Docker**: Refer to [Docker documentation](https://docs.docker.com/)

## License

This project is for educational purposes. Ensure you comply with Spotify's and Deezer's terms of service.
