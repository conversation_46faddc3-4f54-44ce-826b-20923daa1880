@echo off
REM DeemixDownloader Docker Startup Script for Windows

echo 🎵 Starting DeemixDownloader Docker Container
echo ==============================================

REM Check if .env file exists
if not exist .env (
    echo ⚠️  Warning: .env file not found!
    echo    Please copy .env.example to .env and add your Spotify credentials
    echo    copy .env.example .env
    echo.
    set /p continue="Continue anyway? (y/N): "
    if /i not "%continue%"=="y" exit /b 1
)

REM Create downloads directory if it doesn't exist
if not exist downloads mkdir downloads

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

REM Check if docker-compose is available
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ docker-compose not found. Please install Docker Desktop with docker-compose.
    pause
    exit /b 1
)

echo 🚀 Starting container with docker-compose...
docker-compose up -d

if errorlevel 1 (
    echo ❌ Failed to start container
    echo    Check the logs with: docker-compose logs
    pause
    exit /b 1
)

echo.
echo ✅ Container started successfully!
echo.
echo 🌐 Web Interface: http://localhost:10000
echo 📊 Health Check: http://localhost:10000/status
echo.
echo 📋 Useful commands:
echo    View logs:    docker-compose logs -f
echo    Stop:         docker-compose down
echo    Restart:      docker-compose restart
echo.
echo 📁 Downloads will be saved to: .\downloads\
echo.

REM Wait a moment for container to start
timeout /t 3 /nobreak >nul

REM Check if container is healthy
curl -s http://localhost:10000/ >nul 2>&1
if not errorlevel 1 (
    echo 🎉 Container is running and web interface is accessible!
    echo    Opening browser...
    start http://localhost:10000
) else (
    echo ⚠️  Container started but web interface not yet ready
    echo    Please wait a moment and try: http://localhost:10000
)

echo.
echo Press any key to exit...
pause >nul
