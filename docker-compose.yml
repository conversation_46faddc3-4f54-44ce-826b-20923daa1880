version: '3.8'

services:
  deemix-downloader:
    build: .
    container_name: deemix-downloader-web
    ports:
      - "10000:10000"
    volumes:
      # Mount downloads directory (change left path to your desired download location)
      - ./downloads:/app/downloads
      # Mount config directory for persistent configuration
      - ./config:/app/config
      # Optional: Mount a custom DeemixDownloader.py if you want to modify it
      # - ./DeemixDownloader.py:/app/DeemixDownloader.py
    environment:
      # Spotify API credentials (set these in your .env file or here)
      - SPOTIPY_CLIENT_ID=${SPOTIPY_CLIENT_ID:-}
      - SPOTIPY_CLIENT_SECRET=${SPOTIPY_CLIENT_SECRET:-}
      - SPOTIPY_REDIRECT_URI=${SPOTIPY_REDIRECT_URI:-http://localhost:10000/callback}
      # Docker environment flag
      - DOCKER_ENV=true
      # Optional: Set timezone
      - TZ=UTC
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:10000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# Optional: Create a network for the container
networks:
  default:
    name: deemix-network
