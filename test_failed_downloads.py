#!/usr/bin/env python3
"""
Test script for failed downloads tracking functionality in DeemixDownloader.py
This script tests the failed downloads recording and file saving without actually downloading.
"""

import os
import sys
import tempfile
from datetime import datetime

# Add the current directory to the path so we can import DeemixDownloader
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the functions we want to test
from DeemixDownloader import (
    record_failed_download,
    save_failed_downloads,
    failed_downloads,
    download_stats
)

def test_record_failed_download():
    """Test recording failed downloads"""
    print("Testing failed download recording...")
    
    # Clear any existing failed downloads
    failed_downloads.clear()
    
    # Test recording different types of failures
    test_cases = [
        ("Song 1 Artist 1", "No results found on Deezer"),
        ("Song 2 Artist 2", "Download process failed: Command failed"),
        ("Song 3 Artist 3", "Deezer API initialization failed"),
        ("Song 4 Artist 4", "Unexpected error: Network timeout")
    ]
    
    for query, reason in test_cases:
        record_failed_download(query, reason)
    
    # Verify recordings
    assert len(failed_downloads) == 4, f"Expected 4 failed downloads, got {len(failed_downloads)}"
    
    for i, (query, reason) in enumerate(test_cases):
        failure = failed_downloads[i]
        assert failure['query'] == query, f"Expected query '{query}', got '{failure['query']}'"
        assert failure['reason'] == reason, f"Expected reason '{reason}', got '{failure['reason']}'"
        assert 'timestamp' in failure, "Timestamp missing from failure record"
        
        # Verify timestamp format
        try:
            datetime.strptime(failure['timestamp'], '%Y-%m-%d %H:%M:%S')
        except ValueError:
            assert False, f"Invalid timestamp format: {failure['timestamp']}"
    
    print("✅ Failed download recording tests passed!")
    return True

def test_save_failed_downloads_with_failures():
    """Test saving failed downloads file when there are failures"""
    print("\nTesting failed downloads file saving (with failures)...")
    
    # Ensure we have some failed downloads from previous test
    if not failed_downloads:
        record_failed_download("Test Song", "Test Reason")
    
    # Save to file
    save_failed_downloads()
    
    # Check if file was created
    script_dir = os.path.dirname(os.path.abspath(__file__))
    failed_file_path = os.path.join(script_dir, 'failed_downloads.txt')
    
    assert os.path.exists(failed_file_path), f"Failed downloads file not created at {failed_file_path}"
    
    # Read and verify file content
    with open(failed_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for expected content
    assert "Failed Downloads Report" in content, "Missing report header"
    assert "Generated:" in content, "Missing generation timestamp"
    assert f"Total failed downloads: {len(failed_downloads)}" in content, "Missing total count"
    
    # Check that each failure is recorded
    for failure in failed_downloads:
        assert failure['query'] in content, f"Query '{failure['query']}' not found in file"
        assert failure['reason'] in content, f"Reason '{failure['reason']}' not found in file"
        assert failure['timestamp'] in content, f"Timestamp '{failure['timestamp']}' not found in file"
    
    print(f"✅ Failed downloads file created successfully at: {failed_file_path}")
    print(f"   File contains {len(failed_downloads)} failure records")
    
    # Clean up test file
    try:
        os.remove(failed_file_path)
        print("   Test file cleaned up")
    except:
        print("   Warning: Could not clean up test file")
    
    return True

def test_save_failed_downloads_no_failures():
    """Test saving failed downloads file when there are no failures"""
    print("\nTesting failed downloads file saving (no failures)...")
    
    # Clear failed downloads
    failed_downloads.clear()
    
    # Save to file
    save_failed_downloads()
    
    # Check if file was created
    script_dir = os.path.dirname(os.path.abspath(__file__))
    failed_file_path = os.path.join(script_dir, 'failed_downloads.txt')
    
    assert os.path.exists(failed_file_path), f"Failed downloads file not created at {failed_file_path}"
    
    # Read and verify file content
    with open(failed_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for expected content
    assert "Failed Downloads Report" in content, "Missing report header"
    assert "No failed downloads!" in content, "Missing no failures message"
    
    print(f"✅ No-failures file created successfully at: {failed_file_path}")
    
    # Clean up test file
    try:
        os.remove(failed_file_path)
        print("   Test file cleaned up")
    except:
        print("   Warning: Could not clean up test file")
    
    return True

def test_file_format():
    """Test the format of the failed downloads file"""
    print("\nTesting failed downloads file format...")
    
    # Clear and add test data
    failed_downloads.clear()
    record_failed_download("Test Song by Test Artist", "No results found on Deezer")
    record_failed_download("Another Song", "Download process failed: Network error")
    
    # Save to file
    save_failed_downloads()
    
    # Read file and check format
    script_dir = os.path.dirname(os.path.abspath(__file__))
    failed_file_path = os.path.join(script_dir, 'failed_downloads.txt')
    
    with open(failed_file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Check file structure
    assert lines[0].strip() == "Failed Downloads Report", "Incorrect header"
    assert lines[1].startswith("Generated:"), "Missing generation timestamp"
    assert lines[2].strip() == "=" * 60, "Missing separator"
    assert lines[4].strip() == "Total failed downloads: 2", "Incorrect total count"
    
    # Check individual entries format
    content = ''.join(lines)
    assert "1. Query: Test Song by Test Artist" in content, "Missing first query"
    assert "   Reason: No results found on Deezer" in content, "Missing first reason"
    assert "2. Query: Another Song" in content, "Missing second query"
    assert "   Reason: Download process failed: Network error" in content, "Missing second reason"
    
    print("✅ File format tests passed!")
    
    # Clean up test file
    try:
        os.remove(failed_file_path)
        print("   Test file cleaned up")
    except:
        print("   Warning: Could not clean up test file")
    
    return True

def main():
    """Run all tests"""
    print("🧪 Testing Failed Downloads Tracking Functionality")
    print("=" * 55)
    
    try:
        # Run all tests
        test_record_failed_download()
        test_save_failed_downloads_with_failures()
        test_save_failed_downloads_no_failures()
        test_file_format()
        
        print("\n🎉 All failed downloads tracking tests passed!")
        print("The failed downloads functionality is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
