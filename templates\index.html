<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeemixDownloader Web Interface</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .controls {
            padding: 30px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1em;
            border-radius: 25px;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .btn.stop {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        
        .btn.clear {
            background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
        }
        
        .status {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .status-indicator.running {
            background: #28a745;
            animation: pulse 1.5s infinite;
        }
        
        .status-indicator.stopped {
            background: #dc3545;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .output-container {
            padding: 30px;
            background: #1e1e1e;
            color: #f8f8f2;
            font-family: 'Courier New', monospace;
            max-height: 600px;
            overflow-y: auto;
        }
        
        .output-line {
            margin: 5px 0;
            padding: 2px 0;
            line-height: 1.4;
        }
        
        .output-line:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .footer {
            padding: 20px 30px;
            text-align: center;
            color: #666;
            font-size: 0.9em;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 DeemixDownloader</h1>
            <p>Web Interface for Spotify to Deezer Music Downloads</p>
        </div>
        
        <div class="controls">
            <button id="runBtn" class="btn" onclick="runScript()">
                🚀 Run Script
            </button>
            <button id="stopBtn" class="btn stop" onclick="stopScript()" disabled>
                ⏹️ Stop Script
            </button>
            <button id="clearBtn" class="btn clear" onclick="clearOutput()">
                🗑️ Clear Output
            </button>
        </div>
        
        <div class="status">
            <span id="statusIndicator" class="status-indicator stopped"></span>
            <span id="statusText">Ready to run</span>
        </div>
        
        <div class="output-container" id="outputContainer">
            <div class="loading">
                <div>📋 Output will appear here when you run the script</div>
                <div style="margin-top: 10px; font-size: 0.9em; opacity: 0.7;">
                    The script will fetch your Spotify liked songs and playlists, then download them from Deezer
                </div>
            </div>
        </div>
        
        <div class="footer">
            DeemixDownloader Web Interface | Port 10000 | Real-time Output
        </div>
    </div>

    <script>
        let isRunning = false;
        let outputLength = 0;
        let pollInterval;

        function runScript() {
            fetch('/run', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        isRunning = true;
                        updateUI();
                        startPolling();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to start script');
                });
        }

        function stopScript() {
            fetch('/stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // UI will be updated by polling
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to stop script');
                });
        }

        function clearOutput() {
            fetch('/clear', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        document.getElementById('outputContainer').innerHTML = 
                            '<div class="loading"><div>📋 Output cleared - ready for next run</div></div>';
                        outputLength = 0;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

        function updateUI() {
            const runBtn = document.getElementById('runBtn');
            const stopBtn = document.getElementById('stopBtn');
            const statusIndicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');

            if (isRunning) {
                runBtn.disabled = true;
                stopBtn.disabled = false;
                statusIndicator.className = 'status-indicator running';
                statusText.textContent = 'Script is running...';
            } else {
                runBtn.disabled = false;
                stopBtn.disabled = true;
                statusIndicator.className = 'status-indicator stopped';
                statusText.textContent = 'Ready to run';
            }
        }

        function updateOutput(output) {
            const container = document.getElementById('outputContainer');
            
            if (output.length === 0) {
                container.innerHTML = '<div class="loading"><div>📋 No output yet...</div></div>';
                return;
            }

            // Only update if there's new content
            if (output.length > outputLength) {
                let html = '';
                for (let i = 0; i < output.length; i++) {
                    html += `<div class="output-line">${escapeHtml(output[i])}</div>`;
                }
                container.innerHTML = html;
                container.scrollTop = container.scrollHeight;
                outputLength = output.length;
            }
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function pollStatus() {
            fetch('/status')
                .then(response => response.json())
                .then(data => {
                    const wasRunning = isRunning;
                    isRunning = data.running;
                    
                    if (wasRunning !== isRunning) {
                        updateUI();
                    }
                    
                    updateOutput(data.output);
                    
                    if (!isRunning && pollInterval) {
                        clearInterval(pollInterval);
                        pollInterval = null;
                    }
                })
                .catch(error => {
                    console.error('Polling error:', error);
                });
        }

        function startPolling() {
            if (pollInterval) {
                clearInterval(pollInterval);
            }
            pollInterval = setInterval(pollStatus, 1000); // Poll every second
        }

        // Initialize UI
        updateUI();
        
        // Start polling immediately to get current status
        pollStatus();
    </script>
</body>
</html>
