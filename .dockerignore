# Git
.git
.gitignore

# Documentation
README.md
*.md

# Environment files
.env
.env.example

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so

# Virtual environments
venv/
env/
ENV/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Downloads directory (will be mounted)
downloads/

# Test files
test_*
demo_*
*_test.py

# Docker files (not needed in container)
Dockerfile
docker-compose.yml
.dockerignore

# Logs
*.log

# Temporary files
*.tmp
*.temp
